import { useState, useEffect } from 'react';
import { useForm, useField } from '@tanstack/react-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { Link, useNavigate } from '@tanstack/react-router';
import { supabase } from '../../lib/supabase';
import { useCertificate } from '../../contexts/CertificateContext';
import { CheckboxField } from '../../components/ui/CheckboxField';
import { Breadcrumb } from '../../components/ui/Breadcrumb';
import { usePageVisit } from '../../hooks/usePageVisit';
import { useNavigationState } from '../../hooks/useNavigationState';

// Define certificate type
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Define the data structure for the database records
interface CertificateData {
  trinkwarmwasser?: Record<string, any>;
  lueftung?: Record<string, any>;
}

// Define the form schema using Zod
const twwLueftungSchema = z.object({
  // Trinkwarmwasser (TWW) Felder
  // Fields that should only be displayed for WG/B certificate type
  TW_Baujahr: z.string().optional(),
  TW_Speicher_Baujahr: z.string().optional(),
  TW_Verteilung_Baujahr: z.string().optional(),
  TW_Verteilung_Art: z.enum(['0', '1', '2']).default('1').optional(),
  TW_Verteilung_Dämmung: z.enum(['0', '1']).default('1').optional(),
  TW_Zirkulation: z.enum(['0', '1']).default('1').optional(),
  TW_Speicher_Standort: z.enum(['1', '2', '3']).default('2').optional(),
  TW_Technik: z.enum([
    'WT_HZG',
    'WT_EDL',
    'WT_SOLAR',
    'WT_WP',
    'WT_FERNW'
  ]).default('WT_HZG').optional(),

  // Lüftung Felder - only for WG/B certificate type
  Luft_Baujahr: z.string().optional(),
  Luft_Verteilung_Baujahr: z.string().optional(),
  Luft_Lage: z.enum(['0', '1', '2']).default('1').optional(),
  Luft_Typ: z.enum(['0', '1', '2', '3']).default('0').optional(),
});

type TwwLueftungFormValues = z.infer<typeof twwLueftungSchema>;

export const TwwLueftungPage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [certificateType, setCertificateType] = useState<CertificateType | null>(null);
  const { activeCertificateId } = useCertificate();
  const { markPageAsVisited } = useNavigationState(certificateType);

  // Mark this page as visited for navigation tracking
  usePageVisit('tww-lueftung');
  const [initialValues, setInitialValues] = useState<Partial<TwwLueftungFormValues>>({
    TW_Baujahr: '',
    TW_Speicher_Baujahr: '',
    TW_Verteilung_Baujahr: '',
    TW_Verteilung_Art: '1',
    TW_Verteilung_Dämmung: '1',
    TW_Zirkulation: '1',
    TW_Speicher_Standort: '2',
    TW_Technik: 'WT_HZG',
    Luft_Baujahr: '',
    Luft_Verteilung_Baujahr: '',
    Luft_Lage: '1',
    Luft_Typ: '0',
  });
  const [isLoading, setIsLoading] = useState(true);

  // Fetch certificate type
  const { data: certificateData } = useQuery({
    queryKey: ['energieausweise', 'certificate_type', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('certificate_type')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update certificate type when data is fetched
  useEffect(() => {
    if (certificateData?.certificate_type) {
      setCertificateType(certificateData.certificate_type as CertificateType);
    }
  }, [certificateData]);

  // Fetch existing data
  const { data: existingData, isError, error } = useQuery<CertificateData | null>({
    queryKey: ['energieausweise', 'trinkwarmwasser', 'lueftung', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('trinkwarmwasser, lueftung')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data as CertificateData;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update form values when data is fetched
  useEffect(() => {
    if (existingData) {
      // Ensure we're working with objects by using type assertions and defaults
      const trinkwarmwasserData: Record<string, any> = existingData.trinkwarmwasser || {};
      const lueftungData: Record<string, any> = existingData.lueftung || {};

      const combinedData = {
        ...trinkwarmwasserData,
        ...lueftungData
      };

      if (Object.keys(combinedData).length > 0) {
        setInitialValues(prev => ({
          ...prev,
          ...combinedData
        }));
      }
    }
    setIsLoading(false);
  }, [existingData]);

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: TwwLueftungFormValues) => {
      // Split the data into trinkwarmwasser and lueftung fields
      const trinkwarmwasserFields = [
        'TW_Baujahr', 'TW_Speicher_Baujahr', 'TW_Verteilung_Baujahr',
        'TW_Verteilung_Art', 'TW_Verteilung_Dämmung', 'TW_Zirkulation',
        'TW_Speicher_Standort', 'TW_Technik'
        // Removed TW_Solar, HZ_Solar, TW_WP, HZ_WP as they are now in GebaeudedetailsPage2
      ];

      const lueftungFields = [
        'Luft_Baujahr', 'Luft_Verteilung_Baujahr', 'Luft_Lage', 'Luft_Typ'
      ];

      const trinkwarmwasserData: Record<string, any> = {};
      const lueftungData: Record<string, any> = {};

      // Separate the data into the respective objects
      Object.entries(data).forEach(([key, value]) => {
        if (trinkwarmwasserFields.includes(key)) {
          trinkwarmwasserData[key] = value;
        } else if (lueftungFields.includes(key)) {
          lueftungData[key] = value;
        }
      });

      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      // Update the database with the separated data
      const { data: result, error } = await supabase
        .from('energieausweise')
        .update({
          trinkwarmwasser: trinkwarmwasserData,
          lueftung: lueftungData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select();

      if (error) throw error;
      return result;
    },
    onSuccess: async () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise', activeCertificateId] });
      queryClient.invalidateQueries({ queryKey: ['energieausweise', 'trinkwarmwasser', 'lueftung', activeCertificateId] });

      // Navigate based on certificate type
      if (certificateType === 'WG/B') {
        // For WG/B, skip VerbrauchPage and go directly to ZusammenfassungPage
        await markPageAsVisited('zusammenfassung');
        navigate({ to: '/erfassen/zusammenfassung' });
      } else {
        // For WG/V and NWG/V, go to VerbrauchPage
        await markPageAsVisited('verbrauch');
        navigate({ to: '/erfassen/verbrauch' });
      }
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  // Create the form
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);

      // Create a copy of the data to modify
      const dataToSubmit = { ...value };

      // Handle fields based on certificate type
      if (certificateType !== 'WG/B') {
        // Remove WG/B-specific fields for other certificate types
        delete dataToSubmit.TW_Baujahr;
        delete dataToSubmit.TW_Speicher_Baujahr;
        delete dataToSubmit.TW_Verteilung_Baujahr;
        delete dataToSubmit.TW_Verteilung_Art;
        delete dataToSubmit.TW_Verteilung_Dämmung;
        delete dataToSubmit.TW_Zirkulation;
        delete dataToSubmit.TW_Speicher_Standort;
        delete dataToSubmit.TW_Technik;
        delete dataToSubmit.Luft_Baujahr;
        delete dataToSubmit.Luft_Verteilung_Baujahr;
        delete dataToSubmit.Luft_Lage;
        delete dataToSubmit.Luft_Typ;
      }

      saveMutation.mutate(dataToSubmit as TwwLueftungFormValues);
    },
  });

  // Helper component for form fields
  const FormField = ({
    name,
    label,
    type = 'text',
    placeholder = '',
    required = false
  }: {
    name: keyof TwwLueftungFormValues;
    label: string;
    type?: string;
    placeholder?: string;
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <input
          id={name}
          name={name}
          type={type}
          value={state.value ?? ''}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        />
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  // Helper component for select fields
  const SelectField = ({
    name,
    label,
    options,
    required = false
  }: {
    name: keyof TwwLueftungFormValues;
    label: string;
    options: { value: string; label: string }[];
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <select
          id={name}
          name={name}
          value={state.value ?? ''}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Trinkwarmwasser & Lüftung erfassen
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die Daten zu Trinkwarmwasser und Lüftung ein.
      </p>

      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
          {/* Only show these sections for WG/B certificate type */}
          {certificateType === 'WG/B' && (
            <>
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
                  Trinkwarmwasser - Grunddaten
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    name="TW_Baujahr"
                    label="Baujahr Trinkwarmwasseranlage"
                    placeholder="z.B. 2000"
                  />

                  <FormField
                    name="TW_Speicher_Baujahr"
                    label="Baujahr TW-Speicher"
                    placeholder="z.B. 1995"
                  />

                  <FormField
                    name="TW_Verteilung_Baujahr"
                    label="Baujahr TW-Leitungen"
                    placeholder="z.B. 2000"
                  />

                  <SelectField
                    name="TW_Verteilung_Art"
                    label="Art der Verteilung"
                    options={[
                      { value: '0', label: 'Dezentral' },
                      { value: '1', label: 'Gebäudezentral' },
                      { value: '2', label: 'Wohnungszentral' },
                    ]}
                  />
                </div>
              </div>

              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
                  Trinkwarmwasser - Verteilung & Speicherung
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <CheckboxField
                    name="TW_Verteilung_Dämmung"
                    label="Verteilleitungen gedämmt"
                    form={form}
                  />

                  <CheckboxField
                    name="TW_Zirkulation"
                    label="Zirkulierend"
                    form={form}
                  />

                  <SelectField
                    name="TW_Speicher_Standort"
                    label="Standort des Speichers"
                    options={[
                      { value: '1', label: 'Innerhalb der thermischen Hülle' },
                      { value: '2', label: 'Keller' },
                      { value: '3', label: 'Dach' },
                    ]}
                  />

                  <SelectField
                    name="TW_Technik"
                    label="Technik"
                    options={[
                      { value: 'WT_HZG', label: 'Über Heizung' },
                      { value: 'WT_EDL', label: 'Elektrisch direkt' },
                      { value: 'WT_SOLAR', label: 'Solar' },
                      { value: 'WT_WP', label: 'Wärmepumpe' },
                      { value: 'WT_FERNW', label: 'Fernwärme' },
                    ]}
                  />
                </div>
              </div>
            </>
          )}

          {/* Erneuerbare Energien section has been moved to GebaeudedetailsPage2.tsx */}

          {/* Only show Lüftung section for WG/B certificate type */}
          {certificateType === 'WG/B' && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
                Lüftung
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  name="Luft_Baujahr"
                  label="Baujahr Lüftungsanlage"
                  placeholder="z.B. 2010"
                />

                <FormField
                  name="Luft_Verteilung_Baujahr"
                  label="Baujahr Lüftungsverteilung"
                  placeholder="z.B. 2010"
                />

                <SelectField
                  name="Luft_Lage"
                  label="Lage der Lüftungsanlage (Aufstellort)"
                  options={[
                    { value: '0', label: 'Außerhalb der thermischen Hülle' },
                    { value: '1', label: 'Innerhalb der thermischen Hülle' },
                    { value: '2', label: 'Teils innerhalb, teils außerhalb' },
                  ]}
                />

                <SelectField
                  name="Luft_Typ"
                  label="Typ der Lüftungsanlage (Art)"
                  options={[
                    { value: '0', label: 'Keine mechanische Lüftung' },
                    { value: '1', label: 'Abluftanlage' },
                    { value: '2', label: 'Zu- und Abluftanlage ohne WRG' },
                    { value: '3', label: 'Zu- und Abluftanlage mit WRG' },
                  ]}
                />
              </div>
            </div>
          )}

          {submitError && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {submitError}
            </div>
          )}

          <div className="flex justify-between mt-8">
            <Link
              to="/erfassen/heizung"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
            >
              Zurück
            </Link>
            <button
              type="submit"
              disabled={form.state.isSubmitting || saveMutation.isPending}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
            >
              {form.state.isSubmitting || saveMutation.isPending ? 'Wird gespeichert...' : 'Weiter'}
            </button>
          </div>
        </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>Fehler beim Laden der Daten: {error instanceof Error ? error.message : 'Unbekannter Fehler'}</p>
          <p className="mt-2">Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.</p>
        </div>
      )}
    </div>
  );
};